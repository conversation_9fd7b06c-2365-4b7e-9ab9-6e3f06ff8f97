{"name": "backend", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "geoip-lite": "^1.4.10", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.3", "uuid": "^11.1.0"}, "devDependencies": {"@types/express": "^5.0.1"}}